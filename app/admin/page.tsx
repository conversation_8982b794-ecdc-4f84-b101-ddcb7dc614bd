"use client"

import { useState, useEffect } from "react"
import { ThemeAwareDashboard } from "@/components/admin/theme-aware-dashboard"

interface AdminStats {
  totalSongs: number
  totalArtists: number
  songsByLanguage: { tr: number; en: number; ug: number }
  recentActivity: Array<{
    id: string
    type: string
    title: string
    description: string
    timestamp: string
  }>
  monthlyStats: Array<{
    month: string
    songs: number
    artists: number
  }>
  topArtists: Array<{
    id: string
    name: string
    songCount: number
    totalViews?: number
  }>
  popularSongs: Array<{
    id: string
    title: string
    artist: string
    views?: number
    language: string
  }>
  systemHealth: {
    database: 'healthy' | 'warning' | 'error'
    api: 'healthy' | 'warning' | 'error'
    storage: 'healthy' | 'warning' | 'error'
  }
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats>({
    totalSongs: 0,
    totalArtists: 0,
    songsByLanguage: { tr: 0, en: 0, ug: 0 },
    recentActivity: [],
    monthlyStats: [
      { month: 'January', songs: 15, artists: 3 },
      { month: 'February', songs: 12, artists: 2 },
      { month: 'March', songs: 18, artists: 4 },
      { month: 'April', songs: 20, artists: 5 },
      { month: 'May', songs: 25, artists: 6 },
      { month: 'June', songs: 22, artists: 4 }
    ],
    topArtists: [
      { id: '1', name: 'Artist 1', songCount: 15, totalViews: 1250 },
      { id: '2', name: 'Artist 2', songCount: 12, totalViews: 980 },
      { id: '3', name: 'Artist 3', songCount: 8, totalViews: 750 }
    ],
    popularSongs: [
      { id: '1', title: 'Popular Song 1', artist: 'Artist 1', views: 500, language: 'tr' },
      { id: '2', title: 'Popular Song 2', artist: 'Artist 2', views: 450, language: 'en' },
      { id: '3', title: 'Popular Song 3', artist: 'Artist 3', views: 400, language: 'ug' }
    ],
    systemHealth: {
      database: 'healthy',
      api: 'healthy',
      storage: 'healthy'
    }
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('admin_token') || 'admin'

      const response = await fetch('/api/admin/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const adminStats = await response.json()
        // Merge with default data for enhanced features
        setStats(prevStats => ({
          ...prevStats,
          ...adminStats,
          monthlyStats: adminStats.monthlyStats || prevStats.monthlyStats,
          topArtists: adminStats.topArtists || prevStats.topArtists,
          popularSongs: adminStats.popularSongs || prevStats.popularSongs,
          systemHealth: adminStats.systemHealth || prevStats.systemHealth
        }))
      } else {
        console.error('Failed to fetch stats:', response.status)
      }
    } catch (error) {
      console.error('Error fetching admin stats:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <ThemeAwareDashboard
      stats={stats}
      loading={loading}
      onRefresh={fetchStats}
    />
  )
}
