"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON>, usePathname } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { ModeToggle } from "@/components/mode-toggle"

import {
  LayoutDashboard,
  Music,
  Users,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  X,
  Shield,
  Home,
  Plus,
  FileText
} from "lucide-react"

interface AdminLayoutProps {
  children: React.ReactNode
}

const navigation = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
    description: '<PERSON><PERSON> bakı<PERSON>'
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    href: '/admin/songs',
    icon: Music,
    description: '<PERSON><PERSON><PERSON> yönetimi',
    children: [
      { name: 'Tüm<PERSON>', href: '/admin/songs', icon: Music },
      { name: '<PERSON><PERSON>', href: '/admin/songs/new', icon: Plus },
    ]
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    href: '/admin/artists',
    icon: Users,
    description: '<PERSON>at<PERSON><PERSON> yönetimi',
    children: [
      { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/admin/artists', icon: Users },
      { name: 'Yeni', href: '/admin/artists/new', icon: Plus },
    ]
  },
  {
    name: 'İstatistikler',
    href: '/admin/stats',
    icon: BarChart3,
    description: 'Analitik veriler'
  },
  {
    name: 'Ayarlar',
    href: '/admin/settings',
    icon: Settings,
    description: 'Sistem ayarları'
  },
]

function AdminLayoutContent({ children }: AdminLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    const token = localStorage.getItem('admin_token')
    const storedUser = localStorage.getItem('admin_user')

    console.log('Admin Layout - Token check:', { token: !!token, pathname })

    if (!token && pathname !== '/admin/login') {
      console.log('No token, redirecting to login')
      router.push('/admin/login')
    } else {
      setIsAuthenticated(!!token)
      if (storedUser) {
        try {
          setUser(JSON.parse(storedUser))
        } catch (e) {
          console.error('Error parsing stored user:', e)
          localStorage.removeItem('admin_user')
        }
      }
    }
    setLoading(false)
  }, [pathname, router])

  const handleLogout = () => {
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
    setUser(null)
    window.location.href = '/admin/login'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading Admin Panel...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated && pathname !== '/admin/login') {
    return null
  }

  if (pathname === '/admin/login') {
    return (
      <div className="min-h-screen bg-background">
        {children}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-72 bg-card/95 backdrop-blur-sm border-r border-border transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>

        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-4 bg-primary border-b border-border">
          <div className="flex items-center space-x-2">
            <div className="p-1.5 bg-primary-foreground/20 rounded-lg backdrop-blur-sm">
              <Shield className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <span className="text-lg font-bold text-primary-foreground">MusicApp</span>
              <p className="text-xs text-primary-foreground/80 font-medium">Admin</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden text-primary-foreground hover:bg-primary-foreground/20 rounded-lg p-1"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-1 overflow-y-auto max-h-[calc(100vh-180px)]">
          {navigation.map((item) => {
            const isActive = pathname === item.href ||
              (item.href !== '/admin' && pathname.startsWith(item.href))

            return (
              <div key={item.name} className="space-y-1">
                <Link
                  href={item.href}
                  className={`group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-primary text-primary-foreground shadow-lg'
                      : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  }`}
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon className={`mr-3 h-4 w-4 transition-transform duration-200 ${
                    isActive ? 'scale-110' : 'group-hover:scale-105'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{item.name}</div>
                    {item.description && !isActive && (
                      <div className="text-xs opacity-60 truncate">{item.description}</div>
                    )}
                  </div>
                </Link>

                {/* Sub-navigation */}
                {item.children && isActive && (
                  <div className="ml-7 space-y-1 mt-1">
                    {item.children.map((child) => (
                      <Link
                        key={child.href}
                        href={child.href}
                        className={`flex items-center px-2 py-1.5 text-xs rounded-md transition-colors ${
                          pathname === child.href
                            ? 'text-primary font-medium bg-primary/10'
                            : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
                        }`}
                        onClick={() => setSidebarOpen(false)}
                      >
                        <child.icon className="mr-2 h-3 w-3" />
                        <span className="truncate">{child.name}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )
          })}
        </nav>

        {/* User Section */}
        <div className="p-4 border-t border-border bg-muted/50">
          <div className="flex items-center gap-2 mb-3">
            <Avatar className="h-8 w-8 bg-primary">
              <AvatarFallback className="bg-primary text-primary-foreground font-bold text-sm">
                {user?.username?.charAt(0)?.toUpperCase() || 'A'}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate text-foreground">{user?.username || 'Admin'}</p>
              <p className="text-xs text-muted-foreground truncate">{user?.role || 'ADMIN'}</p>
            </div>
          </div>

          <div className="space-y-1">
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start text-muted-foreground hover:text-foreground hover:bg-accent rounded-md h-8 text-xs"
              asChild
            >
              <Link href="/client">
                <Home className="h-3 w-3 mr-2" />
                Ana Site
              </Link>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10 rounded-md h-8 text-xs"
              onClick={handleLogout}
            >
              <LogOut className="h-3 w-3 mr-2" />
              Çıkış
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-72">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-14 shrink-0 items-center gap-x-4 border-b border-border bg-background/80 backdrop-blur-sm px-4 shadow-lg">
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-4 w-4" />
          </Button>

          <div className="flex flex-1 gap-x-4 self-stretch">
            <div className="flex flex-1 items-center">
              <h1 className="text-lg font-semibold text-foreground">
                Admin Panel
              </h1>
            </div>
            <div className="flex items-center gap-x-3">
              <ModeToggle />
              <Badge variant="secondary" className="text-xs">
                Online
              </Badge>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 p-6">
          <div className="mx-auto max-w-7xl">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return <AdminLayoutContent>{children}</AdminLayoutContent>
}
