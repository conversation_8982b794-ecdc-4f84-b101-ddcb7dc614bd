"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { SimpleSongEditor } from "@/components/admin/simple-song-editor"
import { ArrowLeft, Save, Music, Loader2, Sparkles } from "lucide-react"
import Link from "next/link"

interface Artist {
  id: string
  name: string
  slug: string
}

interface Verse {
  id: string
  chords: string
  lyrics: string
  order: number
  type?: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro'
}

export default function NewSongPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [loadingArtists, setLoadingArtists] = useState(true)
  const [error, setError] = useState("")
  const [artists, setArtists] = useState<Artist[]>([])
  const [formData, setFormData] = useState({
    title: "",
    slug: "",
    artistId: "",
    language: "",
    capo: "",
  })
  const [verses, setVerses] = useState<Verse[]>([
    {
      id: Math.random().toString(36).substr(2, 9),
      chords: "",
      lyrics: "",
      order: 1,
      type: 'verse'
    }
  ])

  useEffect(() => {
    fetchArtists()
  }, [])

  const fetchArtists = async () => {
    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch('/api/admin/artists', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setArtists(data)
      }
    } catch (error) {
      console.error('Error fetching artists:', error)
    } finally {
      setLoadingArtists(false)
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }))
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    // Validation
    if (!formData.title || !formData.artistId || !formData.language) {
      setError("Lütfen tüm gerekli alanları doldurun")
      setLoading(false)
      return
    }

    if (verses.some(verse => !verse.chords.trim() || !verse.lyrics.trim())) {
      setError("Lütfen tüm verse alanlarını doldurun")
      setLoading(false)
      return
    }

    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch('/api/admin/songs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          ...formData,
          capo: formData.capo ? parseInt(formData.capo) : null,
          verses
        })
      })

      const data = await response.json()

      if (response.ok) {
        router.push('/admin/songs')
      } else {
        setError(data.error || 'Şarkı oluşturulurken hata oluştu')
      }
    } catch (error) {
      setError('Ağ hatası. Lütfen tekrar deneyin.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/admin/songs">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Şarkılara Dön
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-foreground">Yeni Şarkı Ekle</h1>
          <p className="text-muted-foreground">Gelişmiş editör ile yeni bir şarkı oluşturun</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Music className="h-5 w-5 text-primary" />
              Temel Bilgiler
            </CardTitle>
            <CardDescription>Şarkının temel bilgilerini girin</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Şarkı Adı *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="Şarkı adını girin"
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug *</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="url-slug"
                  required
                  disabled={loading}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="artist">Sanatçı *</Label>
                <Select
                  value={formData.artistId}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, artistId: value }))}
                  disabled={loading || loadingArtists}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sanatçı seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {artists.map((artist) => (
                      <SelectItem key={artist.id} value={artist.id}>
                        {artist.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">Dil *</Label>
                <Select
                  value={formData.language}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, language: value }))}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Dil seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tr">Türkçe</SelectItem>
                    <SelectItem value="en">İngilizce</SelectItem>
                    <SelectItem value="ug">Uygurca</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="capo">Capo</Label>
                <Input
                  id="capo"
                  type="number"
                  min="0"
                  max="12"
                  value={formData.capo}
                  onChange={(e) => setFormData(prev => ({ ...prev, capo: e.target.value }))}
                  placeholder="0"
                  disabled={loading}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Verses */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              Gelişmiş Akor & Şarkı Sözü Editörü
            </CardTitle>
            <CardDescription>
              Akorları şarkı sözlerinin tam üzerine yerleştirin. Tıklayarak konum seçin, akor yazın ve Enter'a basın.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimpleSongEditor
              verses={verses}
              onChange={setVerses}
            />
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center gap-4">
          <Button type="submit" size="lg" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Kaydediliyor...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Şarkıyı Kaydet
              </>
            )}
          </Button>
          <Button type="button" variant="outline" size="lg" asChild>
            <Link href="/admin/songs">İptal</Link>
          </Button>
        </div>
      </form>
    </div>
  )
}
