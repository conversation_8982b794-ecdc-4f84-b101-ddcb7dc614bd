"use client"

import { useState, useEffect } from "react"
import { useRouter, useParams } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { SimpleSongEditor } from "@/components/admin/simple-song-editor"
import { ArrowLeft, Save, Loader2, Music } from "lucide-react"

interface Artist {
  id: string
  name: string
  slug: string
}

interface Verse {
  id: string
  chords: string
  lyrics: string
  order: number
  type?: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro'
}

interface Song {
  id: string
  title: string
  slug: string
  artistId: string
  language: string
  capo?: number
  verses: Verse[]
}

export default function EditSongPage() {
  const router = useRouter()
  const params = useParams()
  const songId = params.id as string
  
  const [loading, setLoading] = useState(false)
  const [loadingData, setLoadingData] = useState(true)
  const [loadingArtists, setLoadingArtists] = useState(true)
  const [error, setError] = useState("")
  const [artists, setArtists] = useState<Artist[]>([])
  const [formData, setFormData] = useState({
    title: "",
    slug: "",
    artistId: "",
    language: "",
    capo: "",
  })
  const [verses, setVerses] = useState<Verse[]>([
    {
      id: Math.random().toString(36).substr(2, 9),
      chords: "",
      lyrics: "",
      order: 1,
      type: 'verse'
    }
  ])

  useEffect(() => {
    fetchArtists()
    fetchSong()
  }, [songId])

  const fetchArtists = async () => {
    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch('/api/artists', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setArtists(data)
      }
    } catch (error) {
      console.error('Error fetching artists:', error)
    } finally {
      setLoadingArtists(false)
    }
  }

  const fetchSong = async () => {
    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch(`/api/songs/${songId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const song = await response.json()
        setFormData({
          title: song.title || "",
          slug: song.slug || "",
          artistId: song.artistId || "",
          language: song.language?.toLowerCase() || "",
          capo: song.capo?.toString() || "",
        })
        
        if (song.verses && song.verses.length > 0) {
          setVerses(song.verses.map((v: any, index: number) => ({
            id: v.id || Math.random().toString(36).substr(2, 9),
            chords: v.chords || "",
            lyrics: v.lyrics || "",
            order: v.order || index + 1,
            type: v.type || 'verse'
          })))
        }
      } else {
        setError('Song not found')
      }
    } catch (error) {
      setError('Failed to load song')
    } finally {
      setLoadingData(false)
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }))
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    // Validation
    if (!formData.title || !formData.artistId || !formData.language) {
      setError("Please fill in all required fields")
      setLoading(false)
      return
    }

    if (verses.some(verse => !verse.chords.trim() || !verse.lyrics.trim())) {
      setError("Please fill in all verse fields")
      setLoading(false)
      return
    }

    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch(`/api/songs/${songId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          ...formData,
          capo: formData.capo ? parseInt(formData.capo) : null,
          verses
        })
      })

      const data = await response.json()

      if (response.ok) {
        router.push('/admin/songs')
      } else {
        setError(data.error || 'Failed to update song')
      }
    } catch (error) {
      setError('Network error. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (loadingData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          <div className="h-8 bg-muted rounded animate-pulse flex-1 max-w-md" />
        </div>
        <Card className="max-w-4xl">
          <CardHeader>
            <div className="h-6 bg-muted rounded animate-pulse" />
            <div className="h-4 bg-muted rounded animate-pulse w-2/3" />
          </CardHeader>
          <CardContent className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse w-1/4" />
                <div className="h-10 bg-muted rounded animate-pulse" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/admin/songs">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-slate-800 dark:text-white">Edit Song</h1>
          <p className="text-slate-600 dark:text-slate-400">Update song information</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Info */}
        <Card className="bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-slate-800 dark:text-white">
              <Music className="h-5 w-5" />
              Song Information
            </CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-400">
              Update the song's basic information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-slate-700 dark:text-slate-300">Song Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="Enter song title"
                  required
                  disabled={loading}
                  className="bg-white dark:bg-slate-700 border-slate-300 dark:border-slate-600"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug" className="text-slate-700 dark:text-slate-300">URL Slug *</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="url-slug"
                  required
                  disabled={loading}
                  className="bg-white dark:bg-slate-700 border-slate-300 dark:border-slate-600"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="artist" className="text-slate-700 dark:text-slate-300">Artist *</Label>
                <Select 
                  value={formData.artistId} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, artistId: value }))}
                  disabled={loading || loadingArtists}
                >
                  <SelectTrigger className="bg-white dark:bg-slate-700 border-slate-300 dark:border-slate-600">
                    <SelectValue placeholder="Select artist" />
                  </SelectTrigger>
                  <SelectContent>
                    {artists.map((artist) => (
                      <SelectItem key={artist.id} value={artist.id}>
                        {artist.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="language" className="text-slate-700 dark:text-slate-300">Language *</Label>
                <Select 
                  value={formData.language} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, language: value }))}
                  disabled={loading}
                >
                  <SelectTrigger className="bg-white dark:bg-slate-700 border-slate-300 dark:border-slate-600">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tr">Turkish</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="ug">Uyghur</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="capo" className="text-slate-700 dark:text-slate-300">Capo</Label>
                <Input
                  id="capo"
                  type="number"
                  min="0"
                  max="12"
                  value={formData.capo}
                  onChange={(e) => setFormData(prev => ({ ...prev, capo: e.target.value }))}
                  placeholder="0"
                  disabled={loading}
                  className="bg-white dark:bg-slate-700 border-slate-300 dark:border-slate-600"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Verses */}
        <Card className="bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
          <CardHeader>
            <CardTitle className="text-slate-800 dark:text-white">Lyrics and Chords</CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-400">
              Use the advanced editor to precisely position chords over lyrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SimpleSongEditor
              verses={verses}
              onChange={setVerses}
            />
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center gap-4">
          <Button type="submit" size="lg" disabled={loading} className="bg-blue-600 hover:bg-blue-700">
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Update Song
              </>
            )}
          </Button>
          <Button type="button" variant="outline" size="lg" asChild>
            <Link href="/admin/songs">Cancel</Link>
          </Button>
        </div>
      </form>
    </div>
  )
}
