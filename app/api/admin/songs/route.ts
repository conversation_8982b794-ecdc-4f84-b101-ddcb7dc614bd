import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyAdminToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Verify admin token
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const user = await verifyAdminToken(token)
    
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get all songs with artist info and verses
    const songs = await prisma.song.findMany({
      include: {
        artist: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        verses: {
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(songs)
  } catch (error) {
    console.error('Error fetching admin songs:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin token
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const user = await verifyAdminToken(token)
    
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    const body = await request.json()
    const { title, slug, artistId, language, capo, verses } = body

    // Validate required fields
    if (!title || !slug || !artistId || !language) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Map language to uppercase enum values
    const languageMap: { [key: string]: string } = {
      'tr': 'TR',
      'en': 'EN',
      'ug': 'UG'
    }

    const mappedLanguage = languageMap[language.toLowerCase()] || language.toUpperCase()
    console.log('Original language:', language)
    console.log('Mapped language:', mappedLanguage)

    // Check if slug already exists
    const existingSong = await prisma.song.findUnique({
      where: { slug }
    })

    if (existingSong) {
      return NextResponse.json(
        { error: 'Song with this slug already exists' },
        { status: 400 }
      )
    }

    // Create song with verses in a transaction
    const song = await prisma.$transaction(async (tx) => {
      // Create the song
      const newSong = await tx.song.create({
        data: {
          title,
          slug,
          artistId,
          language: mappedLanguage as any,
          capo: capo ? parseInt(capo) : null
        }
      })

      // Create verses if provided
      if (verses && verses.length > 0) {
        await tx.songVerse.createMany({
          data: verses.map((verse: any, index: number) => ({
            songId: newSong.id,
            lyrics: verse.lyrics || '',
            chords: verse.chords || '',
            order: index + 1
          }))
        })
      }

      // Return song with artist and verses
      return await tx.song.findUnique({
        where: { id: newSong.id },
        include: {
          artist: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          verses: {
            orderBy: {
              order: 'asc'
            }
          }
        }
      })
    })

    // Log activity
    try {
      await prisma.adminActivity.create({
        data: {
          type: 'SONG_ADDED',
          title: 'Yeni şarkı eklendi',
          description: `"${title}" şarkısı eklendi`,
          timestamp: new Date()
        }
      })
    } catch (logError) {
      console.error('Error logging activity:', logError)
    }

    return NextResponse.json(song, { status: 201 })
  } catch (error) {
    console.error('Error creating song:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
