import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyAdminToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Verify admin token
    const authHeader = request.headers.get('authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const user = await verifyAdminToken(token)

    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get total counts
    const [totalSongs, totalArtists] = await Promise.all([
      prisma.song.count(),
      prisma.artist.count()
    ])

    // Get songs by language
    const songsByLanguage = await prisma.song.groupBy({
      by: ['language'],
      _count: {
        id: true
      }
    })

    const languageStats = {
      TR: 0,
      EN: 0,
      UG: 0
    }

    songsByLanguage.forEach(item => {
      languageStats[item.language as keyof typeof languageStats] = item._count.id
    })

    // Get recent activities
    const recentActivity = await prisma.adminActivity.findMany({
      take: 10,
      orderBy: {
        timestamp: 'desc'
      }
    })

    // Get top artists by song count
    const topArtists = await prisma.artist.findMany({
      include: {
        _count: {
          select: {
            songs: true
          }
        }
      },
      orderBy: {
        songs: {
          _count: 'desc'
        }
      },
      take: 5
    })

    // Get monthly stats (last 6 months)
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)

    const monthlyStats = await prisma.song.groupBy({
      by: ['createdAt'],
      _count: {
        id: true
      },
      where: {
        createdAt: {
          gte: sixMonthsAgo
        }
      }
    })

    // Process monthly stats
    const monthlyData = []
    for (let i = 5; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      const monthName = date.toLocaleDateString('en-US', { month: 'long' })

      const songsInMonth = monthlyStats.filter(stat => {
        const statDate = new Date(stat.createdAt)
        return statDate.getMonth() === date.getMonth() && statDate.getFullYear() === date.getFullYear()
      }).reduce((sum, stat) => sum + stat._count.id, 0)

      monthlyData.push({
        month: monthName,
        songs: songsInMonth,
        artists: Math.floor(songsInMonth / 3) // Estimate artists based on songs
      })
    }

    // Get popular songs (mock data for now)
    const popularSongs = await prisma.song.findMany({
      include: {
        artist: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    })

    const stats = {
      totalSongs,
      totalArtists,
      songsByLanguage: {
        tr: languageStats.TR,
        en: languageStats.EN,
        ug: languageStats.UG
      },
      recentActivity: recentActivity.map(activity => ({
        id: activity.id,
        type: activity.type.toLowerCase().replace('_', '_') as any,
        title: activity.title,
        description: activity.description,
        timestamp: activity.timestamp
      })),
      monthlyStats: monthlyData,
      topArtists: topArtists.map(artist => ({
        id: artist.id,
        name: artist.name,
        songCount: artist._count.songs,
        totalViews: Math.floor(Math.random() * 1000) + 100 // Mock data
      })),
      popularSongs: popularSongs.map((song, index) => ({
        id: song.id,
        title: song.title,
        artist: song.artist.name,
        views: Math.floor(Math.random() * 500) + 100, // Mock data
        language: song.language.toLowerCase()
      })),
      systemHealth: {
        database: 'healthy' as const,
        api: 'healthy' as const,
        storage: 'healthy' as const
      }
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Error fetching admin stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
