import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyAdminToken } from '@/lib/auth'

// GET song by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const song = await prisma.song.findUnique({
      where: { id },
      include: {
        artist: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        verses: {
          orderBy: {
            order: 'asc'
          }
        }
      }
    })

    if (!song) {
      return NextResponse.json(
        { error: 'Song not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(song)
  } catch (error) {
    console.error('Error fetching song:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT update song
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin token
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const user = await verifyAdminToken(token)

    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    const { id } = await params
    const { title, slug, artistId, capo, language, verses } = await request.json()

    if (!title || !slug || !artistId || !language || !verses || !Array.isArray(verses)) {
      return NextResponse.json(
        { error: 'Title, slug, artistId, language and verses are required' },
        { status: 400 }
      )
    }

    // Map language to uppercase enum values
    const languageMap: { [key: string]: string } = {
      'tr': 'TR',
      'en': 'EN',
      'ug': 'UG'
    }

    const mappedLanguage = languageMap[language.toLowerCase()] || language.toUpperCase()

    // Check if slug already exists (excluding current song)
    const existingSong = await prisma.song.findFirst({
      where: {
        slug,
        NOT: { id }
      }
    })

    if (existingSong) {
      return NextResponse.json(
        { error: 'Song with this slug already exists' },
        { status: 409 }
      )
    }

    // Verify artist exists
    const artist = await prisma.artist.findUnique({
      where: { id: artistId }
    })

    if (!artist) {
      return NextResponse.json(
        { error: 'Artist not found' },
        { status: 404 }
      )
    }

    // Update song with transaction
    const song = await prisma.$transaction(async (tx) => {
      // Delete existing verses
      await tx.songVerse.deleteMany({
        where: { songId: id }
      })

      // Update song
      const updatedSong = await tx.song.update({
        where: { id },
        data: {
          title,
          slug,
          artistId,
          capo,
          language: mappedLanguage as any,
        },
        include: {
          artist: true,
          verses: {
            orderBy: {
              order: 'asc'
            }
          }
        }
      })

      // Create new verses
      await tx.songVerse.createMany({
        data: verses.map((verse: any, index: number) => ({
          songId: id,
          chords: verse.chords,
          lyrics: verse.lyrics,
          order: index
        }))
      })

      // Get updated song with verses
      return await tx.song.findUnique({
        where: { id },
        include: {
          artist: true,
          verses: {
            orderBy: {
              order: 'asc'
            }
          }
        }
      })
    })

    // Log activity
    await prisma.adminActivity.create({
      data: {
        type: 'SONG_UPDATED',
        title: 'Song Updated',
        description: `Song "${title}" by ${artist.name} was updated by ${user.username}`
      }
    })

    return NextResponse.json(song)

  } catch (error) {
    console.error('Error updating song:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE song
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin token
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const user = await verifyAdminToken(token)

    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    const { id } = await params

    // Get song info before deletion
    const song = await prisma.song.findUnique({
      where: { id },
      include: {
        artist: {
          select: { name: true }
        }
      }
    })

    if (!song) {
      return NextResponse.json(
        { error: 'Song not found' },
        { status: 404 }
      )
    }

    // Delete song (verses will be deleted due to cascade)
    await prisma.song.delete({
      where: { id }
    })

    // Log activity
    await prisma.adminActivity.create({
      data: {
        type: 'SONG_DELETED',
        title: 'Song Deleted',
        description: `Song "${song.title}" by ${song.artist.name} was deleted by ${user.username}`
      }
    })

    return NextResponse.json({ message: 'Song deleted successfully' })

  } catch (error) {
    console.error('Error deleting song:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
