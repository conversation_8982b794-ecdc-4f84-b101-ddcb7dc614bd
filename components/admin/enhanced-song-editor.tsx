"use client"

import React, { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Music,
  Plus,
  Trash2,
  Eye,
  Save,
  RotateCcw,
  Move,
  Copy,
  Lightbulb,
  Wand2,
  ArrowUp,
  ArrowDown,
  Play,
  Edit
} from "lucide-react"
// Note: DragDropContext requires client-side rendering
// import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"

interface Verse {
  id: string
  lyrics: string
  chords: string
  order: number
  type?: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro'
}

interface EnhancedSongEditorProps {
  verses: Verse[]
  onChange: (verses: Verse[]) => void
  autoSave?: boolean
  onAutoSave?: (verses: Verse[]) => void
}

const CHORD_SUGGESTIONS = [
  'C', 'G', 'Am', 'F', 'Dm', 'Em', 'A', 'D', 'E', 'B',
  'C#', 'F#', 'G#', 'A#', 'D#', 'Cm', 'Gm', 'Fm', 'Bm'
]

const VERSE_TYPES = [
  { value: 'verse', label: 'Verse', color: 'bg-blue-500' },
  { value: 'chorus', label: 'Chorus', color: 'bg-green-500' },
  { value: 'bridge', label: 'Bridge', color: 'bg-purple-500' },
  { value: 'intro', label: 'Intro', color: 'bg-orange-500' },
  { value: 'outro', label: 'Outro', color: 'bg-red-500' }
]

const SONG_TEMPLATES = [
  {
    name: 'Basic Song',
    verses: [
      { type: 'verse', lyrics: 'Verse 1 lyrics here...', chords: 'C G Am F' },
      { type: 'chorus', lyrics: 'Chorus lyrics here...', chords: 'F C G Am' },
      { type: 'verse', lyrics: 'Verse 2 lyrics here...', chords: 'C G Am F' },
      { type: 'chorus', lyrics: 'Chorus lyrics here...', chords: 'F C G Am' }
    ]
  },
  {
    name: 'Extended Song',
    verses: [
      { type: 'intro', lyrics: 'Intro...', chords: 'C G' },
      { type: 'verse', lyrics: 'Verse 1...', chords: 'C G Am F' },
      { type: 'chorus', lyrics: 'Chorus...', chords: 'F C G Am' },
      { type: 'verse', lyrics: 'Verse 2...', chords: 'C G Am F' },
      { type: 'chorus', lyrics: 'Chorus...', chords: 'F C G Am' },
      { type: 'bridge', lyrics: 'Bridge...', chords: 'Am F C G' },
      { type: 'chorus', lyrics: 'Chorus...', chords: 'F C G Am' },
      { type: 'outro', lyrics: 'Outro...', chords: 'C G C' }
    ]
  }
]

export function EnhancedSongEditor({ 
  verses, 
  onChange, 
  autoSave = false, 
  onAutoSave 
}: EnhancedSongEditorProps) {
  const [activeTab, setActiveTab] = useState("editor")
  const [selectedVerse, setSelectedVerse] = useState<string | null>(null)
  const [showChordSuggestions, setShowChordSuggestions] = useState(false)
  const [realTimePreview, setRealTimePreview] = useState(true)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && onAutoSave && verses.length > 0) {
      const timer = setTimeout(() => {
        onAutoSave(verses)
        setLastSaved(new Date())
      }, 2000)
      return () => clearTimeout(timer)
    }
  }, [verses, autoSave, onAutoSave])

  const generateId = () => Math.random().toString(36).substr(2, 9)

  const addVerse = (type: string = 'verse') => {
    const newVerse: Verse = {
      id: generateId(),
      lyrics: '',
      chords: '',
      order: verses.length + 1,
      type: type as any
    }
    onChange([...verses, newVerse])
    setSelectedVerse(newVerse.id)
  }

  const updateVerse = (id: string, field: keyof Verse, value: string) => {
    const updatedVerses = verses.map(verse =>
      verse.id === id ? { ...verse, [field]: value } : verse
    )
    onChange(updatedVerses)
  }

  const deleteVerse = (id: string) => {
    const updatedVerses = verses.filter(verse => verse.id !== id)
      .map((verse, index) => ({ ...verse, order: index + 1 }))
    onChange(updatedVerses)
    if (selectedVerse === id) {
      setSelectedVerse(null)
    }
  }

  const duplicateVerse = (id: string) => {
    const verse = verses.find(v => v.id === id)
    if (verse) {
      const newVerse: Verse = {
        ...verse,
        id: generateId(),
        order: verses.length + 1
      }
      onChange([...verses, newVerse])
    }
  }

  const moveVerse = (id: string, direction: 'up' | 'down') => {
    const currentIndex = verses.findIndex(v => v.id === id)
    if (currentIndex === -1) return

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    if (newIndex < 0 || newIndex >= verses.length) return

    const newVerses = [...verses]
    const [movedVerse] = newVerses.splice(currentIndex, 1)
    newVerses.splice(newIndex, 0, movedVerse)
    
    // Update order
    const updatedVerses = newVerses.map((verse, index) => ({
      ...verse,
      order: index + 1
    }))
    
    onChange(updatedVerses)
  }

  // Simplified reordering without drag & drop for now
  const reorderVerses = (fromIndex: number, toIndex: number) => {
    const items = Array.from(verses)
    const [reorderedItem] = items.splice(fromIndex, 1)
    items.splice(toIndex, 0, reorderedItem)

    // Update order
    const updatedVerses = items.map((verse, index) => ({
      ...verse,
      order: index + 1
    }))

    onChange(updatedVerses)
  }

  const applyTemplate = (template: typeof SONG_TEMPLATES[0]) => {
    const newVerses: Verse[] = template.verses.map((verse, index) => ({
      id: generateId(),
      lyrics: verse.lyrics,
      chords: verse.chords,
      order: index + 1,
      type: verse.type as any
    }))
    onChange(newVerses)
  }

  const insertChord = (verseId: string, chord: string) => {
    const verse = verses.find(v => v.id === verseId)
    if (verse) {
      const newChords = verse.chords ? `${verse.chords} ${chord}` : chord
      updateVerse(verseId, 'chords', newChords)
    }
  }

  const getVerseTypeInfo = (type?: string) => {
    return VERSE_TYPES.find(t => t.value === type) || VERSE_TYPES[0]
  }

  return (
    <div className="space-y-6">
      {/* Header with Auto-save Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold">Enhanced Song Editor</h3>
          {autoSave && lastSaved && (
            <Badge variant="outline" className="text-xs">
              <Save className="h-3 w-3 mr-1" />
              Saved {lastSaved.toLocaleTimeString()}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Label htmlFor="realtime-preview" className="text-sm">Real-time Preview</Label>
          <Switch
            id="realtime-preview"
            checked={realTimePreview}
            onCheckedChange={setRealTimePreview}
          />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="editor">Editor</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="tools">Tools</TabsTrigger>
        </TabsList>

        <TabsContent value="editor" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Verse List */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Song Structure</span>
                  <div className="flex gap-2">
                    {VERSE_TYPES.slice(0, 3).map(type => (
                      <Button
                        key={type.value}
                        size="sm"
                        variant="outline"
                        onClick={() => addVerse(type.value)}
                        className="text-xs"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        {type.label}
                      </Button>
                    ))}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {verses.map((verse, index) => {
                    const typeInfo = getVerseTypeInfo(verse.type)
                    return (
                      <div
                        key={verse.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-all ${
                          selectedVerse === verse.id
                            ? 'border-primary bg-primary/5'
                            : 'border-border hover:border-primary/50'
                        }`}
                        onClick={() => setSelectedVerse(verse.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Move className="h-4 w-4 text-muted-foreground" />
                            <Badge className={`${typeInfo.color} text-white text-xs`}>
                              {typeInfo.label}
                            </Badge>
                            <span className="text-sm font-medium">
                              {verse.lyrics.split('\n')[0].substring(0, 30)}
                              {verse.lyrics.length > 30 ? '...' : ''}
                            </span>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation()
                                moveVerse(verse.id, 'up')
                              }}
                              disabled={index === 0}
                            >
                              <ArrowUp className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation()
                                moveVerse(verse.id, 'down')
                              }}
                              disabled={index === verses.length - 1}
                            >
                              <ArrowDown className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation()
                                duplicateVerse(verse.id)
                              }}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation()
                                deleteVerse(verse.id)
                              }}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {verses.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Music className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No verses yet. Add your first verse!</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Verse Editor */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {selectedVerse ? 'Edit Verse' : 'Select a verse to edit'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {selectedVerse ? (
                  <VerseEditor
                    verse={verses.find(v => v.id === selectedVerse)!}
                    onUpdate={(field, value) => updateVerse(selectedVerse, field, value)}
                    chordSuggestions={CHORD_SUGGESTIONS}
                    onInsertChord={(chord) => insertChord(selectedVerse, chord)}
                    showSuggestions={showChordSuggestions}
                    onToggleSuggestions={setShowChordSuggestions}
                  />
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Edit className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>Select a verse from the left to start editing</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="preview">
          <SongPreview verses={verses} realTime={realTimePreview} />
        </TabsContent>

        <TabsContent value="templates">
          <TemplateSelector templates={SONG_TEMPLATES} onApply={applyTemplate} />
        </TabsContent>

        <TabsContent value="tools">
          <SongTools verses={verses} onChange={onChange} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Verse Editor Component
interface VerseEditorProps {
  verse: Verse
  onUpdate: (field: keyof Verse, value: string) => void
  chordSuggestions: string[]
  onInsertChord: (chord: string) => void
  showSuggestions: boolean
  onToggleSuggestions: (show: boolean) => void
}

function VerseEditor({
  verse,
  onUpdate,
  chordSuggestions,
  onInsertChord,
  showSuggestions,
  onToggleSuggestions
}: VerseEditorProps) {
  return (
    <div className="space-y-4">
      {/* Verse Type */}
      <div>
        <Label>Verse Type</Label>
        <select
          value={verse.type || 'verse'}
          onChange={(e) => onUpdate('type', e.target.value)}
          className="w-full mt-1 p-2 border rounded-md"
        >
          {VERSE_TYPES.map(type => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>
      </div>

      {/* Chords */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label>Chords</Label>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onToggleSuggestions(!showSuggestions)}
          >
            <Lightbulb className="h-3 w-3 mr-1" />
            Suggestions
          </Button>
        </div>
        <Textarea
          value={verse.chords}
          onChange={(e) => onUpdate('chords', e.target.value)}
          placeholder="C G Am F"
          className="font-mono"
          rows={3}
        />
        {showSuggestions && (
          <div className="mt-2 flex flex-wrap gap-1">
            {chordSuggestions.map(chord => (
              <Button
                key={chord}
                size="sm"
                variant="outline"
                onClick={() => onInsertChord(chord)}
                className="text-xs"
              >
                {chord}
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Lyrics */}
      <div>
        <Label>Lyrics</Label>
        <Textarea
          value={verse.lyrics}
          onChange={(e) => onUpdate('lyrics', e.target.value)}
          placeholder="Enter lyrics here..."
          rows={6}
          className="mt-1"
        />
      </div>
    </div>
  )
}

// Song Preview Component
interface SongPreviewProps {
  verses: Verse[]
  realTime: boolean
}

function SongPreview({ verses, realTime }: SongPreviewProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Song Preview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6 max-h-96 overflow-y-auto">
          {verses.map((verse, index) => {
            const typeInfo = getVerseTypeInfo(verse.type)
            return (
              <div key={verse.id} className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge className={`${typeInfo.color} text-white text-xs`}>
                    {typeInfo.label} {index + 1}
                  </Badge>
                </div>
                <div className="font-mono text-sm space-y-1 p-3 bg-muted rounded-lg">
                  <div className="text-primary font-semibold leading-relaxed">
                    {verse.chords || ' '}
                  </div>
                  <div className="leading-relaxed whitespace-pre-wrap">
                    {verse.lyrics || ' '}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        {verses.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Music className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No verses to preview</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Template Selector Component
interface TemplateSelectorProps {
  templates: typeof SONG_TEMPLATES
  onApply: (template: typeof SONG_TEMPLATES[0]) => void
}

function TemplateSelector({ templates, onApply }: TemplateSelectorProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {templates.map((template, index) => (
        <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-lg">{template.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 mb-4">
              {template.verses.map((verse, vIndex) => (
                <div key={vIndex} className="flex items-center gap-2">
                  <Badge
                    className={`${getVerseTypeInfo(verse.type).color} text-white text-xs`}
                  >
                    {getVerseTypeInfo(verse.type).label}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {verse.lyrics.substring(0, 20)}...
                  </span>
                </div>
              ))}
            </div>
            <Button
              onClick={() => onApply(template)}
              className="w-full"
            >
              <Wand2 className="h-4 w-4 mr-2" />
              Apply Template
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// Song Tools Component
interface SongToolsProps {
  verses: Verse[]
  onChange: (verses: Verse[]) => void
}

function SongTools({ verses, onChange }: SongToolsProps) {
  const exportSong = () => {
    const songData = {
      verses,
      exportedAt: new Date().toISOString()
    }
    const blob = new Blob([JSON.stringify(songData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'song-export.json'
    a.click()
    URL.revokeObjectURL(url)
  }

  const clearAll = () => {
    if (confirm('Are you sure you want to clear all verses?')) {
      onChange([])
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader>
          <CardTitle>Export & Import</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button onClick={exportSong} className="w-full" variant="outline">
            Export Song Data
          </Button>
          <Button className="w-full" variant="outline">
            Import Song Data
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button onClick={clearAll} variant="destructive" className="w-full">
            <Trash2 className="h-4 w-4 mr-2" />
            Clear All Verses
          </Button>
          <Button className="w-full" variant="outline">
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Last Saved
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
