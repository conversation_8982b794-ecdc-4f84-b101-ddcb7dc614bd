"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Music,
  Users,
  TrendingUp,
  Clock,
  Plus,
  Eye,
  Edit,
  Trash2,
  BarChart3,
  Calendar,
  Globe,
  Activity,
  Star,
  Download,
  Upload,
  Settings,
  AlertCircle,
  CheckCircle,
  XCircle
} from "lucide-react"

interface DashboardStats {
  totalSongs: number
  totalArtists: number
  songsByLanguage: { tr: number; en: number; ug: number }
  recentActivity: ActivityItem[]
  monthlyStats: MonthlyStats[]
  topArtists: TopArtist[]
  popularSongs: PopularSong[]
  systemHealth: SystemHealth
}

interface ActivityItem {
  id: string
  type: 'SONG_ADDED' | 'ARTIST_ADDED' | 'SONG_UPDATED' | 'ARTIST_UPDATED' | 'SONG_DELETED' | 'ARTIST_DELETED'
  title: string
  description: string
  timestamp: string
}

interface MonthlyStats {
  month: string
  songs: number
  artists: number
}

interface TopArtist {
  id: string
  name: string
  songCount: number
  totalViews?: number
}

interface PopularSong {
  id: string
  title: string
  artist: string
  views?: number
  language: string
}

interface SystemHealth {
  database: 'healthy' | 'warning' | 'error'
  api: 'healthy' | 'warning' | 'error'
  storage: 'healthy' | 'warning' | 'error'
}

interface EnhancedDashboardProps {
  stats: DashboardStats
  loading: boolean
  onRefresh: () => void
}

export function EnhancedDashboard({ stats, loading, onRefresh }: EnhancedDashboardProps) {
  const [activeTab, setActiveTab] = useState("overview")

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'SONG_ADDED': return <Plus className="h-4 w-4 text-green-500" />
      case 'ARTIST_ADDED': return <Users className="h-4 w-4 text-blue-500" />
      case 'SONG_UPDATED': return <Edit className="h-4 w-4 text-yellow-500" />
      case 'ARTIST_UPDATED': return <Edit className="h-4 w-4 text-yellow-500" />
      case 'SONG_DELETED': return <Trash2 className="h-4 w-4 text-red-500" />
      case 'ARTIST_DELETED': return <Trash2 className="h-4 w-4 text-red-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getLanguageColor = (language: string) => {
    switch (language) {
      case 'tr': return 'bg-red-500'
      case 'en': return 'bg-blue-500'
      case 'ug': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const getLanguageName = (language: string) => {
    switch (language) {
      case 'tr': return 'Türkçe'
      case 'en': return 'English'
      case 'ug': return 'ئۇيغۇرچە'
      default: return language.toUpperCase()
    }
  }

  if (loading) {
    return <DashboardSkeleton />
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Enhanced Dashboard</h1>
          <p className="text-slate-400">Comprehensive overview of your music app</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={onRefresh} variant="outline" className="border-slate-600 text-slate-300">
            <Activity className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button asChild className="bg-blue-600 hover:bg-blue-700">
            <Link href="/admin/songs/new">
              <Plus className="h-4 w-4 mr-2" />
              New Song
            </Link>
          </Button>
          <Button variant="outline" asChild className="border-slate-600 text-slate-300 hover:bg-slate-700">
            <Link href="/admin/artists/new">
              <Plus className="h-4 w-4 mr-2" />
              New Artist
            </Link>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total Songs"
              value={stats.totalSongs}
              icon={<Music className="h-6 w-6 text-blue-400" />}
              trend="+12%"
              trendUp={true}
            />
            <MetricCard
              title="Total Artists"
              value={stats.totalArtists}
              icon={<Users className="h-6 w-6 text-green-400" />}
              trend="+8%"
              trendUp={true}
            />
            <MetricCard
              title="Languages"
              value={3}
              icon={<Globe className="h-6 w-6 text-purple-400" />}
              trend="Stable"
              trendUp={null}
            />
            <MetricCard
              title="This Month"
              value={stats.monthlyStats?.[0]?.songs || 0}
              icon={<Calendar className="h-6 w-6 text-orange-400" />}
              trend="+25%"
              trendUp={true}
            />
          </div>

          {/* Language Distribution */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Language Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(stats.songsByLanguage).map(([lang, count]) => {
                  const total = Object.values(stats.songsByLanguage).reduce((a, b) => a + b, 0)
                  const percentage = total > 0 ? (count / total) * 100 : 0
                  return (
                    <div key={lang} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={`${getLanguageColor(lang)} text-white text-xs`}>
                            {lang.toUpperCase()}
                          </Badge>
                          <span className="text-slate-300">{getLanguageName(lang)}</span>
                        </div>
                        <span className="text-white font-semibold">{count} songs</span>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button asChild className="h-20 flex-col bg-blue-600 hover:bg-blue-700">
                  <Link href="/admin/songs/new">
                    <Plus className="h-6 w-6 mb-2" />
                    Add Song
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-20 flex-col border-slate-600 text-slate-300 hover:bg-slate-700">
                  <Link href="/admin/artists/new">
                    <Users className="h-6 w-6 mb-2" />
                    Add Artist
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-20 flex-col border-slate-600 text-slate-300 hover:bg-slate-700">
                  <Link href="/admin/songs">
                    <Music className="h-6 w-6 mb-2" />
                    Manage Songs
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-20 flex-col border-slate-600 text-slate-300 hover:bg-slate-700">
                  <Link href="/admin/stats">
                    <BarChart3 className="h-6 w-6 mb-2" />
                    View Stats
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity Preview */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Activity
                </span>
                <Button asChild variant="outline" size="sm" className="border-slate-600 text-slate-300">
                  <Link href="#" onClick={() => setActiveTab("activity")}>
                    View All
                  </Link>
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.recentActivity.slice(0, 5).map((activity) => (
                  <div key={activity.id} className="flex items-center gap-3 p-3 bg-slate-700 rounded-lg">
                    {getActivityIcon(activity.type)}
                    <div className="flex-1">
                      <p className="text-white text-sm font-medium">{activity.title}</p>
                      <p className="text-slate-400 text-xs">{activity.description}</p>
                    </div>
                    <span className="text-slate-400 text-xs">
                      {new Date(activity.timestamp).toLocaleDateString()}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <AnalyticsTab stats={stats} />
        </TabsContent>

        <TabsContent value="activity">
          <ActivityTab activities={stats.recentActivity} />
        </TabsContent>

        <TabsContent value="system">
          <SystemTab health={stats.systemHealth} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Metric Card Component
interface MetricCardProps {
  title: string
  value: number
  icon: React.ReactNode
  trend?: string
  trendUp?: boolean | null
}

function MetricCard({ title, value, icon, trend, trendUp }: MetricCardProps) {
  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-slate-400 text-sm">{title}</p>
            <p className="text-2xl font-bold text-white">{value}</p>
            {trend && (
              <div className="flex items-center gap-1 mt-1">
                {trendUp !== null && (
                  <TrendingUp className={`h-3 w-3 ${trendUp ? 'text-green-400' : 'text-red-400'} ${!trendUp ? 'rotate-180' : ''}`} />
                )}
                <span className={`text-xs ${trendUp === true ? 'text-green-400' : trendUp === false ? 'text-red-400' : 'text-slate-400'}`}>
                  {trend}
                </span>
              </div>
            )}
          </div>
          <div className="p-3 bg-slate-700 rounded-lg">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Analytics Tab Component
function AnalyticsTab({ stats }: { stats: DashboardStats }) {
  return (
    <div className="space-y-6">
      {/* Top Artists */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Star className="h-5 w-5" />
            Top Artists
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.topArtists?.map((artist, index) => (
              <div key={artist.id} className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
                <div className="flex items-center gap-3">
                  <Badge className="bg-blue-600 text-white">#{index + 1}</Badge>
                  <span className="text-white font-medium">{artist.name}</span>
                </div>
                <div className="text-right">
                  <p className="text-white font-semibold">{artist.songCount} songs</p>
                  {artist.totalViews && (
                    <p className="text-slate-400 text-xs">{artist.totalViews} views</p>
                  )}
                </div>
              </div>
            )) || (
              <p className="text-slate-400 text-center py-4">No artist data available</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Popular Songs */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Music className="h-5 w-5" />
            Popular Songs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.popularSongs?.map((song, index) => (
              <div key={song.id} className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
                <div className="flex items-center gap-3">
                  <Badge className="bg-green-600 text-white">#{index + 1}</Badge>
                  <div>
                    <p className="text-white font-medium">{song.title}</p>
                    <p className="text-slate-400 text-sm">by {song.artist}</p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge className={`${getLanguageColor(song.language)} text-white text-xs mb-1`}>
                    {song.language.toUpperCase()}
                  </Badge>
                  {song.views && (
                    <p className="text-slate-400 text-xs">{song.views} views</p>
                  )}
                </div>
              </div>
            )) || (
              <p className="text-slate-400 text-center py-4">No song data available</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Growth */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Monthly Growth
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats.monthlyStats?.map((month, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-slate-300">{month.month}</span>
                  <div className="flex gap-4">
                    <span className="text-blue-400">{month.songs} songs</span>
                    <span className="text-green-400">{month.artists} artists</span>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <Progress value={(month.songs / 20) * 100} className="h-2" />
                  <Progress value={(month.artists / 10) * 100} className="h-2" />
                </div>
              </div>
            )) || (
              <p className="text-slate-400 text-center py-4">No monthly data available</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Activity Tab Component
function ActivityTab({ activities }: { activities: ActivityItem[] }) {
  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Activity className="h-5 w-5" />
          All Activities
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-center gap-3 p-3 bg-slate-700 rounded-lg">
              {getActivityIcon(activity.type)}
              <div className="flex-1">
                <p className="text-white text-sm font-medium">{activity.title}</p>
                <p className="text-slate-400 text-xs">{activity.description}</p>
              </div>
              <div className="text-right">
                <span className="text-slate-400 text-xs">
                  {new Date(activity.timestamp).toLocaleDateString()}
                </span>
                <br />
                <span className="text-slate-500 text-xs">
                  {new Date(activity.timestamp).toLocaleTimeString()}
                </span>
              </div>
            </div>
          ))}
          {activities.length === 0 && (
            <p className="text-slate-400 text-center py-8">No activities yet</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// System Tab Component
function SystemTab({ health }: { health: SystemHealth }) {
  return (
    <div className="space-y-6">
      {/* System Health */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
              <div className="flex items-center gap-3">
                {getHealthIcon(health.database)}
                <span className="text-white">Database</span>
              </div>
              <Badge className={health.database === 'healthy' ? 'bg-green-600' : health.database === 'warning' ? 'bg-yellow-600' : 'bg-red-600'}>
                {health.database}
              </Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
              <div className="flex items-center gap-3">
                {getHealthIcon(health.api)}
                <span className="text-white">API</span>
              </div>
              <Badge className={health.api === 'healthy' ? 'bg-green-600' : health.api === 'warning' ? 'bg-yellow-600' : 'bg-red-600'}>
                {health.api}
              </Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
              <div className="flex items-center gap-3">
                {getHealthIcon(health.storage)}
                <span className="text-white">Storage</span>
              </div>
              <Badge className={health.storage === 'healthy' ? 'bg-green-600' : health.storage === 'warning' ? 'bg-yellow-600' : 'bg-red-600'}>
                {health.storage}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Actions */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">System Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="h-16 flex-col border-slate-600 text-slate-300 hover:bg-slate-700">
              <Download className="h-6 w-6 mb-2" />
              Export Data
            </Button>
            <Button variant="outline" className="h-16 flex-col border-slate-600 text-slate-300 hover:bg-slate-700">
              <Upload className="h-6 w-6 mb-2" />
              Import Data
            </Button>
            <Button variant="outline" className="h-16 flex-col border-slate-600 text-slate-300 hover:bg-slate-700">
              <Settings className="h-6 w-6 mb-2" />
              System Settings
            </Button>
            <Button variant="outline" className="h-16 flex-col border-slate-600 text-slate-300 hover:bg-slate-700">
              <Activity className="h-6 w-6 mb-2" />
              View Logs
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Dashboard Skeleton
function DashboardSkeleton() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <div className="h-8 w-64 bg-slate-700 rounded animate-pulse" />
          <div className="h-4 w-48 bg-slate-700 rounded animate-pulse mt-2" />
        </div>
        <div className="flex gap-2">
          <div className="h-10 w-24 bg-slate-700 rounded animate-pulse" />
          <div className="h-10 w-32 bg-slate-700 rounded animate-pulse" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="h-16 bg-slate-700 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Helper functions need to be accessible
function getActivityIcon(type: string) {
  switch (type) {
    case 'SONG_ADDED': return <Plus className="h-4 w-4 text-green-500" />
    case 'ARTIST_ADDED': return <Users className="h-4 w-4 text-blue-500" />
    case 'SONG_UPDATED': return <Edit className="h-4 w-4 text-yellow-500" />
    case 'ARTIST_UPDATED': return <Edit className="h-4 w-4 text-yellow-500" />
    case 'SONG_DELETED': return <Trash2 className="h-4 w-4 text-red-500" />
    case 'ARTIST_DELETED': return <Trash2 className="h-4 w-4 text-red-500" />
    default: return <Activity className="h-4 w-4 text-gray-500" />
  }
}

function getHealthIcon(status: string) {
  switch (status) {
    case 'healthy': return <CheckCircle className="h-4 w-4 text-green-500" />
    case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-500" />
    case 'error': return <XCircle className="h-4 w-4 text-red-500" />
    default: return <AlertCircle className="h-4 w-4 text-gray-500" />
  }
}

function getLanguageColor(language: string) {
  switch (language) {
    case 'tr': return 'bg-red-500'
    case 'en': return 'bg-blue-500'
    case 'ug': return 'bg-green-500'
    default: return 'bg-gray-500'
  }
}
