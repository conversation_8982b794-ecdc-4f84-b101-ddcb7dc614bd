"use client"

import React, { useState } from "react"
import <PERSON> from "next/link"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Music,
  Users,
  Globe,
  Activity,
  TrendingUp,
  TrendingDown,
  Calendar,
  Clock,
  Plus,
  BarChart3,
  Settings,
  Database,
  Server,
  HardDrive,
  CheckCircle,
  AlertTriangle,
  XCircle
} from "lucide-react"

interface AdminStats {
  totalSongs: number
  totalArtists: number
  songsByLanguage: { tr: number; en: number; ug: number }
  recentActivity: Array<{
    id: string
    type: string
    title: string
    description: string
    timestamp: string
  }>
  monthlyStats: Array<{
    month: string
    songs: number
    artists: number
  }>
  topArtists: Array<{
    id: string
    name: string
    songCount: number
    totalViews?: number
  }>
  popularSongs: Array<{
    id: string
    title: string
    artist: string
    views?: number
    language: string
  }>
  systemHealth: {
    database: 'healthy' | 'warning' | 'error'
    api: 'healthy' | 'warning' | 'error'
    storage: 'healthy' | 'warning' | 'error'
  }
}

interface ThemeAwareDashboardProps {
  stats: AdminStats
  loading: boolean
  onRefresh: () => void
}

// Metric Card Component
interface MetricCardProps {
  title: string
  value: number | string
  icon: React.ReactNode
  trend?: string
  trendUp?: boolean | null
}

function MetricCard({ title, value, icon, trend, trendUp }: MetricCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {trend && (
              <p className={`text-xs flex items-center gap-1 ${
                trendUp === true ? 'text-green-500' : 
                trendUp === false ? 'text-red-500' : 'text-muted-foreground'
              }`}>
                {trendUp === true && <TrendingUp className="h-3 w-3" />}
                {trendUp === false && <TrendingDown className="h-3 w-3" />}
                {trend}
              </p>
            )}
          </div>
          <div className="p-3 bg-muted rounded-lg">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function ThemeAwareDashboard({ stats, loading, onRefresh }: ThemeAwareDashboardProps) {
  const [activeTab, setActiveTab] = useState("overview")

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "song_added":
      case "song_updated":
        return <Music className="h-4 w-4 text-blue-500" />
      case "artist_added":
      case "artist_updated":
        return <Users className="h-4 w-4 text-green-500" />
      default:
        return <Activity className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getLanguageColor = (lang: string) => {
    switch (lang) {
      case 'tr': return 'bg-red-500'
      case 'en': return 'bg-blue-500'
      case 'ug': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const getLanguageName = (lang: string) => {
    switch (lang) {
      case 'tr': return 'Turkish'
      case 'en': return 'English'
      case 'ug': return 'Uyghur'
      default: return lang
    }
  }

  const getSystemHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <CheckCircle className="h-4 w-4 text-muted-foreground" />
    }
  }

  const formatTimeAgo = (date: Date | string) => {
    const now = new Date()
    const targetDate = typeof date === 'string' ? new Date(date) : date
    const diffInMinutes = Math.floor((now.getTime() - targetDate.getTime()) / (1000 * 60))

    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)} hours ago`
    } else {
      return `${Math.floor(diffInMinutes / 1440)} days ago`
    }
  }

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-4 bg-muted rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded animate-pulse mb-2" />
                <div className="h-3 bg-muted rounded animate-pulse w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Welcome to MusicApp admin panel</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={onRefresh} variant="outline">
            <Activity className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button asChild>
            <Link href="/admin/songs/new">
              <Plus className="h-4 w-4 mr-2" />
              New Song
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/artists/new">
              <Plus className="h-4 w-4 mr-2" />
              New Artist
            </Link>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total Songs"
              value={stats.totalSongs}
              icon={<Music className="h-6 w-6 text-blue-500" />}
              trend="+12%"
              trendUp={true}
            />
            <MetricCard
              title="Total Artists"
              value={stats.totalArtists}
              icon={<Users className="h-6 w-6 text-green-500" />}
              trend="+8%"
              trendUp={true}
            />
            <MetricCard
              title="Languages"
              value={3}
              icon={<Globe className="h-6 w-6 text-purple-500" />}
              trend="Stable"
              trendUp={null}
            />
            <MetricCard
              title="This Month"
              value={stats.monthlyStats?.[0]?.songs || 0}
              icon={<Calendar className="h-6 w-6 text-orange-500" />}
              trend="+25%"
              trendUp={true}
            />
          </div>

          {/* Language Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Language Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(stats.songsByLanguage).map(([lang, count]) => {
                  const total = Object.values(stats.songsByLanguage).reduce((a, b) => a + b, 0)
                  const percentage = total > 0 ? (count / total) * 100 : 0
                  return (
                    <div key={lang} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={`${getLanguageColor(lang)} text-white text-xs`}>
                            {lang.toUpperCase()}
                          </Badge>
                          <span>{getLanguageName(lang)}</span>
                        </div>
                        <span className="font-semibold">{count} songs</span>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button asChild variant="outline" className="h-20 flex-col">
                  <Link href="/admin/songs/new">
                    <Plus className="h-6 w-6 mb-2" />
                    Add Song
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-20 flex-col">
                  <Link href="/admin/artists/new">
                    <Plus className="h-6 w-6 mb-2" />
                    Add Artist
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-20 flex-col">
                  <Link href="/admin/songs">
                    <Music className="h-6 w-6 mb-2" />
                    Manage Songs
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-20 flex-col">
                  <Link href="/admin/stats">
                    <BarChart3 className="h-6 w-6 mb-2" />
                    View Stats
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Top Artists */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Top Artists
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.topArtists.map((artist, index) => (
                  <div key={artist.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{artist.name}</p>
                        <p className="text-sm text-muted-foreground">{artist.songCount} songs</p>
                      </div>
                    </div>
                    <Badge variant="secondary">{artist.totalViews || 0} views</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Popular Songs */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Music className="h-5 w-5" />
                Popular Songs
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.popularSongs.map((song, index) => (
                  <div key={song.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{song.title}</p>
                        <p className="text-sm text-muted-foreground">by {song.artist}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={`${getLanguageColor(song.language)} text-white text-xs`}>
                        {song.language.toUpperCase()}
                      </Badge>
                      <Badge variant="secondary">{song.views || 0} views</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                    {getActivityIcon(activity.type)}
                    <div className="flex-1 min-w-0">
                      <p className="font-medium">{activity.title}</p>
                      <p className="text-sm text-muted-foreground">{activity.description}</p>
                      <p className="text-xs text-muted-foreground mt-1">{formatTimeAgo(activity.timestamp)}</p>
                    </div>
                  </div>
                ))}
                {stats.recentActivity.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No recent activity</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                System Health
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Database className="h-5 w-5" />
                    <span>Database</span>
                  </div>
                  {getSystemHealthIcon(stats.systemHealth.database)}
                </div>
                <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Server className="h-5 w-5" />
                    <span>API</span>
                  </div>
                  {getSystemHealthIcon(stats.systemHealth.api)}
                </div>
                <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <HardDrive className="h-5 w-5" />
                    <span>Storage</span>
                  </div>
                  {getSystemHealthIcon(stats.systemHealth.storage)}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
