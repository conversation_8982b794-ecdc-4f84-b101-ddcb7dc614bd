"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { 
  Music, 
  Plus, 
  Trash2, 
  Eye, 
  ArrowUp,
  ArrowDown,
  Copy
} from "lucide-react"

interface Verse {
  id: string
  lyrics: string
  chords: string
  order: number
  type?: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro'
}

interface SimpleSongEditorProps {
  verses: Verse[]
  onChange: (verses: Verse[]) => void
}

const VERSE_TYPES = [
  { value: 'verse', label: 'Verse', color: 'bg-blue-500' },
  { value: 'chorus', label: 'Chorus', color: 'bg-green-500' },
  { value: 'bridge', label: 'Bridge', color: 'bg-purple-500' },
  { value: 'intro', label: 'Intro', color: 'bg-orange-500' },
  { value: 'outro', label: 'Outro', color: 'bg-red-500' }
]

const CHORD_SUGGESTIONS = [
  'C', 'G', 'Am', 'F', 'Dm', 'Em', 'A', 'D', 'E', 'B',
  'C#', 'F#', 'G#', 'A#', 'D#', 'Cm', 'Gm', 'Fm', 'Bm'
]

export function SimpleSongEditor({ verses, onChange }: SimpleSongEditorProps) {
  const [selectedVerse, setSelectedVerse] = useState<string | null>(null)
  const [showChordSuggestions, setShowChordSuggestions] = useState(false)

  const generateId = () => Math.random().toString(36).substr(2, 9)

  const getVerseTypeInfo = (type?: string) => {
    return VERSE_TYPES.find(t => t.value === type) || VERSE_TYPES[0]
  }

  const addVerse = (type: string = 'verse') => {
    const newVerse: Verse = {
      id: generateId(),
      lyrics: '',
      chords: '',
      order: verses.length + 1,
      type: type as any
    }
    onChange([...verses, newVerse])
    setSelectedVerse(newVerse.id)
  }

  const updateVerse = (id: string, field: keyof Verse, value: string) => {
    const updatedVerses = verses.map(verse =>
      verse.id === id ? { ...verse, [field]: value } : verse
    )
    onChange(updatedVerses)
  }

  const deleteVerse = (id: string) => {
    const updatedVerses = verses.filter(verse => verse.id !== id)
      .map((verse, index) => ({ ...verse, order: index + 1 }))
    onChange(updatedVerses)
    if (selectedVerse === id) {
      setSelectedVerse(null)
    }
  }

  const duplicateVerse = (id: string) => {
    const verse = verses.find(v => v.id === id)
    if (verse) {
      const newVerse: Verse = {
        ...verse,
        id: generateId(),
        order: verses.length + 1
      }
      onChange([...verses, newVerse])
    }
  }

  const moveVerse = (id: string, direction: 'up' | 'down') => {
    const currentIndex = verses.findIndex(v => v.id === id)
    if (currentIndex === -1) return

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    if (newIndex < 0 || newIndex >= verses.length) return

    const newVerses = [...verses]
    const [movedVerse] = newVerses.splice(currentIndex, 1)
    newVerses.splice(newIndex, 0, movedVerse)
    
    // Update order
    const updatedVerses = newVerses.map((verse, index) => ({
      ...verse,
      order: index + 1
    }))
    
    onChange(updatedVerses)
  }

  const insertChord = (verseId: string, chord: string) => {
    const verse = verses.find(v => v.id === verseId)
    if (verse) {
      const newChords = verse.chords ? `${verse.chords} ${chord}` : chord
      updateVerse(verseId, 'chords', newChords)
    }
  }

  const selectedVerseData = verses.find(v => v.id === selectedVerse)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Song Editor</h3>
        <div className="flex gap-2">
          {VERSE_TYPES.slice(0, 3).map(type => (
            <Button
              key={type.value}
              size="sm"
              variant="outline"
              onClick={() => addVerse(type.value)}
              className="text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              {type.label}
            </Button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Verse List */}
        <Card>
          <CardHeader>
            <CardTitle>Song Structure</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {verses.map((verse, index) => {
                const typeInfo = getVerseTypeInfo(verse.type)
                return (
                  <div
                    key={verse.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-all ${
                      selectedVerse === verse.id 
                        ? 'border-primary bg-primary/5' 
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedVerse(verse.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge className={`${typeInfo.color} text-white text-xs`}>
                          {typeInfo.label}
                        </Badge>
                        <span className="text-sm font-medium">
                          {verse.lyrics.split('\n')[0].substring(0, 30)}
                          {verse.lyrics.length > 30 ? '...' : ''}
                        </span>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            moveVerse(verse.id, 'up')
                          }}
                          disabled={index === 0}
                        >
                          <ArrowUp className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            moveVerse(verse.id, 'down')
                          }}
                          disabled={index === verses.length - 1}
                        >
                          <ArrowDown className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            duplicateVerse(verse.id)
                          }}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteVerse(verse.id)
                          }}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {verses.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Music className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No verses yet. Add your first verse!</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Verse Editor */}
        <Card>
          <CardHeader>
            <CardTitle>
              {selectedVerse ? 'Edit Verse' : 'Select a verse to edit'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedVerseData ? (
              <div className="space-y-4">
                {/* Verse Type */}
                <div>
                  <Label>Verse Type</Label>
                  <select
                    value={selectedVerseData.type || 'verse'}
                    onChange={(e) => updateVerse(selectedVerse!, 'type', e.target.value)}
                    className="w-full mt-1 p-2 border rounded-md"
                  >
                    {VERSE_TYPES.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Chords */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>Chords</Label>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setShowChordSuggestions(!showChordSuggestions)}
                    >
                      Suggestions
                    </Button>
                  </div>
                  <Textarea
                    value={selectedVerseData.chords}
                    onChange={(e) => updateVerse(selectedVerse!, 'chords', e.target.value)}
                    placeholder="C G Am F"
                    className="font-mono"
                    rows={3}
                  />
                  {showChordSuggestions && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {CHORD_SUGGESTIONS.map(chord => (
                        <Button
                          key={chord}
                          size="sm"
                          variant="outline"
                          onClick={() => insertChord(selectedVerse!, chord)}
                          className="text-xs"
                        >
                          {chord}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Lyrics */}
                <div>
                  <Label>Lyrics</Label>
                  <Textarea
                    value={selectedVerseData.lyrics}
                    onChange={(e) => updateVerse(selectedVerse!, 'lyrics', e.target.value)}
                    placeholder="Enter lyrics here..."
                    rows={6}
                    className="mt-1"
                  />
                </div>

                {/* Preview */}
                <div>
                  <Label>Preview</Label>
                  <div className="mt-1 p-3 bg-muted rounded-lg">
                    <div className="font-mono text-sm space-y-1">
                      <div className="text-primary font-semibold leading-relaxed">
                        {selectedVerseData.chords || ' '}
                      </div>
                      <div className="leading-relaxed whitespace-pre-wrap">
                        {selectedVerseData.lyrics || ' '}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Select a verse from the left to start editing</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
