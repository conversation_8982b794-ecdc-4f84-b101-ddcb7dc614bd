export interface AdminUser {
  id: string
  username: string
  email: string
  role: "admin" | "editor"
  lastLogin: Date
  avatar?: string
}

export interface LoginCredentials {
  username: string
  password: string
}

// Mock admin users - gerçek uygulamada veritabanından gelecek
const adminUsers: AdminUser[] = [
  {
    id: "1",
    username: "admin",
    email: "<EMAIL>",
    role: "admin",
    lastLogin: new Date(),
  },
  {
    id: "2",
    username: "editor",
    email: "<EMAIL>",
    role: "editor",
    lastLogin: new Date(),
  },
]

// Database authentication
export async function authenticateAdmin(username: string, password: string): Promise<AdminUser | null> {
  try {
    // Import prisma here to avoid client-side issues
    const { prisma } = await import('@/lib/prisma')
    const bcrypt = await import('bcryptjs')

    const user = await prisma.user.findUnique({
      where: { username }
    })

    if (user && user.role === 'ADMIN') {
      // Check if password is hashed or plain text
      let isValidPassword = false

      if (user.password.startsWith('$2')) {
        // Password is hashed
        isValidPassword = await bcrypt.compare(password, user.password)
      } else {
        // Password is plain text (for development)
        isValidPassword = user.password === password
      }

      if (isValidPassword) {
        return {
          id: user.id,
          username: user.username,
          email: user.email,
          role: 'admin',
          lastLogin: new Date()
        }
      }
    }

    return null
  } catch (error) {
    console.error('Auth error:', error)
    return null
  }
}

export function getStoredAdmin(): AdminUser | null {
  if (typeof window === "undefined") return null

  const stored = localStorage.getItem("admin_user")
  return stored ? JSON.parse(stored) : null
}

export function storeAdmin(user: AdminUser): void {
  if (typeof window === "undefined") return

  localStorage.setItem("admin_user", JSON.stringify(user))
}

export function clearStoredAdmin(): void {
  if (typeof window === "undefined") return

  localStorage.removeItem("admin_user")
}

// Server-side token verification
export async function verifyAdminToken(token: string): Promise<AdminUser | null> {
  // In a real app, this would verify JWT token
  // For now, we'll use a simple mock verification

  if (!token) {
    return null
  }

  // Mock: assume token is user ID for simplicity
  // In real app, decode JWT and get user from database
  try {
    // For demo purposes, accept 'admin' token as valid admin user
    if (token === 'admin') {
      return {
        id: 'admin',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        lastLogin: new Date()
      }
    }

    const userId = token
    const user = adminUsers.find(u => u.id === userId)
    return user || null
  } catch (error) {
    return null
  }
}

// Generate mock token (in real app, this would be JWT)
export function generateAdminToken(user: AdminUser): string {
  // In real app, generate JWT token
  // For demo purposes, return 'admin' for admin user
  if (user.username === 'admin') {
    return 'admin'
  }
  return user.id
}
